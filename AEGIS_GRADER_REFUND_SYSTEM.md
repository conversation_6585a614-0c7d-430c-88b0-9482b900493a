# AegisGrader Refund System Documentation (Simplified Lambda-First Approach)

## Overview

The AegisGrader system supports automatic refunds for failed grading attempts. The Lambda function handles all processing and refunds directly, keeping the backend simple and efficient.

## Simplified Workflow

### 1. Initial Credit Deduction
- When users request presigned URLs for grading, credits are deducted upfront (1 credit per answer sheet)
- Credit information is included in the manifest file for Lambda to use
- No AegisGrader record is created at this stage

### 2. Lambda Processing
- Lambda processes the grading and creates the AegisGrader document directly in MongoDB
- Lambda calculates failed evaluations and calls the refund API if needed
- All processing happens in Lambda to avoid stressing the backend server

### 3. Automatic Refund Processing
- Lambda calls `/api/aegisGrader/lambda-refunds` endpoint for failed sheets
- Credits are refunded automatically (1 credit per failed sheet)
- Refund transactions are created and linked to the original transaction

## Enhanced Manifest Structure

The manifest now includes credit information for Lambda:

```javascript
{
  "testDetails": { ... },
  "files": [ ... ],
  "creditInfo": {
    "userId": "60f1b2b3c4d5e6f7a8b9c0d1",
    "userType": "Teacher",
    "totalCreditsCharged": 5,
    "originalTransactionId": "usage_abc123",
    "creditsPerSheet": 1
  }
}
```

## AegisGrader Model Structure (Created by Lambda)

```javascript
{
  testDetails: { ... },
  answerSheets: [
    {
      id: String,
      studentName: String,
      rollNumber: String,
      pdfUrl: String,
      timestamp: Number,
      className: String,
      evaluationResult: Mixed,
      status: {
        type: String,
        enum: ['pending', 'processing', 'completed', 'error'],
        default: 'pending'
      },
      processedAt: Date
    }
  ],
  processingStats: {
    totalAnswerSheets: Number,
    successfulEvaluations: Number,
    failedEvaluations: Number,
    completedAt: Date,
    processingStartedAt: Date,
    overallStatus: String
  },
  creditInfo: {
    totalCreditsCharged: Number,
    creditsRefunded: Number,
    originalTransactionId: String,
    refundTransactionIds: [String]
  }
}
```

## API Endpoints

### POST /api/aegisGrader/lambda-refunds
Simple endpoint for Lambda to process refunds.

**Request Body:**
```javascript
{
  "creditInfo": {
    "userId": "60f1b2b3c4d5e6f7a8b9c0d1",
    "userType": "Teacher",
    "originalTransactionId": "usage_abc123"
  },
  "failedSheetCount": 2,
  "submissionId": "60f1b2b3c4d5e6f7a8b9c0d2",
  "failedSheetIds": ["sheet_001", "sheet_002"]
}
```

**Response:**
```javascript
{
  "message": "Refund processed successfully",
  "success": true,
  "refundAmount": 2,
  "newBalance": 98,
  "failedSheetsCount": 2,
  "transaction": { ... }
}
```

### POST /api/aegisGrader/processing-results (Optional)
Optional endpoint for Lambda to notify completion (for monitoring).

**Request Body:**
```javascript
{
  "submissionId": "...",
  "processingStats": { ... }
}
```

### GET /api/aegisGrader/submissions/:userId
Returns all submissions with processing status and refund information.

## Lambda Integration

### Lambda Workflow
1. **Read Manifest**: Lambda reads the manifest file which contains credit information
2. **Process Grading**: Lambda processes all answer sheets and determines success/failure
3. **Create Document**: Lambda creates the AegisGrader document directly in MongoDB
4. **Process Refunds**: If there are failed sheets, Lambda calls the refund API

### Example Lambda Refund Call
```javascript
// In Lambda function after processing
const failedSheets = answerSheets.filter(sheet => sheet.status === 'error');

if (failedSheets.length > 0) {
  const refundPayload = {
    creditInfo: manifest.creditInfo, // From the manifest
    failedSheetCount: failedSheets.length,
    submissionId: createdSubmission._id, // ID of the created AegisGrader document
    failedSheetIds: failedSheets.map(sheet => sheet.id) // Array of failed sheet IDs
  };

  // Call the refund API
  await axios.post(`${API_BASE_URL}/api/aegisGrader/lambda-refunds`, refundPayload);
}
```

## Credit Service Methods

#### `refundCredits(userId, userType, creditAmount, refundDetails)`
Core refund functionality for any failed operations.

#### `processLambdaRefunds(creditInfo, failedSheetCount)`
Simple method for Lambda to process refunds with credit info and failed count.

#### `determineUserType(userId)`
Determines if a user is a Student or Teacher by checking both collections.

## Benefits

1. **Simplified Architecture**: Lambda handles everything directly, reducing backend complexity
2. **Automatic Refunds**: Users automatically get credits back for failed processing
3. **Fair Billing**: Users only pay for successful evaluations
4. **Scalable**: Lambda processes refunds without stressing the main server
5. **Audit Trail**: Complete transaction history for credits and refunds

## Key Simplifications

1. **No Backend Document Creation**: AegisGrader documents are created only by Lambda
2. **Credit Info in Manifest**: All necessary credit information is passed to Lambda
3. **Direct Refund API**: Simple endpoint for Lambda to process refunds
4. **Reduced Complexity**: Removed complex backend processing logic
